{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9f9cf45e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_88e3134f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "x3r6LX1CZOGcE7UGKXsvCpr3nnCVnIpAhUl5gY6dNOE=", "__NEXT_PREVIEW_MODE_ID": "51e29d9ca5080f737f2f04e9e9d4b836", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "166da02eaf5df0c04f379fa135f4a221c1d60e5bc268fb2ff7e58b0713016dae", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3712aca30ae9e45a425897d267e716b7cc378c7850fb376b68e51f8b0918be5f"}}}, "sortedMiddleware": ["/"], "functions": {}}