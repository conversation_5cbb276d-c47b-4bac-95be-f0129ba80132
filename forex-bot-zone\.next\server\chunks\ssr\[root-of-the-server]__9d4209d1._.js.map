{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number | string): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(Number(price))\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).trim() + '...'\n}\n\nexport function generateOrderNumber(): string {\n  const timestamp = Date.now().toString()\n  const random = Math.random().toString(36).substring(2, 8).toUpperCase()\n  return `FBZ-${timestamp.slice(-6)}-${random}`\n}\n\nexport function generateAffiliateCode(username: string): string {\n  const random = Math.random().toString(36).substring(2, 6).toUpperCase()\n  const userPart = username.slice(0, 4).toUpperCase()\n  return `${userPart}${random}`\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAsB;IAChD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC,OAAO;AACnB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ;IACrC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;IACrE,OAAO,CAAC,IAAI,EAAE,UAAU,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,QAAQ;AAC/C;AAEO,SAAS,sBAAsB,QAAgB;IACpD,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;IACrE,MAAM,WAAW,SAAS,KAAK,CAAC,GAAG,GAAG,WAAW;IACjD,OAAO,GAAG,WAAW,QAAQ;AAC/B", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'\n    \n    const variants = {\n      primary: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 focus:ring-blue-500',\n      secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizes = {\n      sm: 'px-3 py-2 text-sm',\n      md: 'px-6 py-3 text-base',\n      lg: 'px-8 py-4 text-lg'\n    }\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          isLoading && 'cursor-wait',\n          className\n        )}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport default Button\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,eACb;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,0BACC;;8BACE,8OAAC;oBAAI,WAAU;oBAAkC,OAAM;oBAA6B,MAAK;oBAAO,SAAQ;;sCACtG,8OAAC;4BAAO,WAAU;4BAAa,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,QAAO;4BAAe,aAAY;;;;;;sCACxF,8OAAC;4BAAK,WAAU;4BAAa,MAAK;4BAAe,GAAE;;;;;;;;;;;;gBAC/C;;2BAIR;;;;;;AAIR;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/app/page.tsx"], "sourcesContent": ["import Button from '@/components/ui/Button'\nimport Link from 'next/link'\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen\">\n      {/* Hero Section */}\n      <section className=\"relative bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 sm:py-32\">\n        <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h1 className=\"text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl\">\n              Trade Smart, Trade{' '}\n              <span className=\"gradient-text\">Confident</span>\n            </h1>\n            <p className=\"mt-6 text-lg leading-8 text-gray-600\">\n              Your trusted source for premium forex trading tools, expert advisors, and indicators.\n              Join thousands of successful traders who trust our professional-grade solutions.\n            </p>\n            <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n              <Link href=\"/shop\">\n                <Button size=\"lg\">\n                  Shop Now\n                </Button>\n              </Link>\n              <Link href=\"/membership\">\n                <Button variant=\"outline\" size=\"lg\">\n                  Get Unlimited Access\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Features Section */}\n      <section className=\"py-24 sm:py-32\">\n        <div className=\"mx-auto max-w-7xl px-6 lg:px-8\">\n          <div className=\"mx-auto max-w-2xl lg:text-center\">\n            <h2 className=\"text-base font-semibold leading-7 text-blue-600\">Premium Trading Tools</h2>\n            <p className=\"mt-2 text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl\">\n              Everything you need to succeed in forex trading\n            </p>\n            <p className=\"mt-6 text-lg leading-8 text-gray-600\">\n              Professional-grade expert advisors, indicators, and trading tools designed for serious traders.\n            </p>\n          </div>\n          <div className=\"mx-auto mt-16 max-w-2xl sm:mt-20 lg:mt-24 lg:max-w-4xl\">\n            <dl className=\"grid max-w-xl grid-cols-1 gap-x-8 gap-y-10 lg:max-w-none lg:grid-cols-2 lg:gap-y-16\">\n              <div className=\"relative pl-16\">\n                <dt className=\"text-base font-semibold leading-7 text-gray-900\">\n                  <div className=\"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600\">\n                    <svg className=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  Expert Advisors\n                </dt>\n                <dd className=\"mt-2 text-base leading-7 text-gray-600\">\n                  Automated trading systems for MT4 and MT5 platforms with proven track records.\n                </dd>\n              </div>\n              <div className=\"relative pl-16\">\n                <dt className=\"text-base font-semibold leading-7 text-gray-900\">\n                  <div className=\"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600\">\n                    <svg className=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 013 19.875v-6.75zM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V8.625zM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 01-1.125-1.125V4.125z\" />\n                    </svg>\n                  </div>\n                  Custom Indicators\n                </dt>\n                <dd className=\"mt-2 text-base leading-7 text-gray-600\">\n                  Advanced technical indicators to enhance your trading analysis and decision making.\n                </dd>\n              </div>\n              <div className=\"relative pl-16\">\n                <dt className=\"text-base font-semibold leading-7 text-gray-900\">\n                  <div className=\"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600\">\n                    <svg className=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M18 18.72a9.094 9.094 0 003.741-.479 3 3 0 00-4.682-2.72m.94 3.198l.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0112 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 016 18.719m12 0a5.971 5.971 0 00-.941-3.197m0 0A5.995 5.995 0 0012 12.75a5.995 5.995 0 00-5.058 2.772m0 0a3 3 0 00-4.681 2.72 8.986 8.986 0 003.74.477m.94-3.197a5.971 5.971 0 00-.94 3.197M15 6.75a3 3 0 11-6 0 3 3 0 016 0zm6 3a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0zm-13.5 0a2.25 2.25 0 11-4.5 0 2.25 2.25 0 014.5 0z\" />\n                    </svg>\n                  </div>\n                  Professional Support\n                </dt>\n                <dd className=\"mt-2 text-base leading-7 text-gray-600\">\n                  Expert support team to help you with installation, setup, and optimization.\n                </dd>\n              </div>\n              <div className=\"relative pl-16\">\n                <dt className=\"text-base font-semibold leading-7 text-gray-900\">\n                  <div className=\"absolute left-0 top-0 flex h-10 w-10 items-center justify-center rounded-lg bg-blue-600\">\n                    <svg className=\"h-6 w-6 text-white\" fill=\"none\" viewBox=\"0 0 24 24\" strokeWidth=\"1.5\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" d=\"M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z\" />\n                    </svg>\n                  </div>\n                  Secure Downloads\n                </dt>\n                <dd className=\"mt-2 text-base leading-7 text-gray-600\">\n                  All products are thoroughly tested and delivered through secure download links.\n                </dd>\n              </div>\n            </dl>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"bg-gray-900\">\n        <div className=\"px-6 py-24 sm:px-6 sm:py-32 lg:px-8\">\n          <div className=\"mx-auto max-w-2xl text-center\">\n            <h2 className=\"text-3xl font-bold tracking-tight text-white sm:text-4xl\">\n              Ready to boost your trading performance?\n            </h2>\n            <p className=\"mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300\">\n              Join thousands of successful traders who trust our premium tools and expert advisors.\n            </p>\n            <div className=\"mt-10 flex items-center justify-center gap-x-6\">\n              <Link href=\"/membership\">\n                <Button size=\"lg\">\n                  Get Started Today\n                </Button>\n              </Link>\n              <Link href=\"/contact\" className=\"text-sm font-semibold leading-6 text-white\">\n                Contact us <span aria-hidden=\"true\">→</span>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;oCAA8D;oCACvD;kDACnB,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,8OAAC;gCAAE,WAAU;0CAAuC;;;;;;0CAIpD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,MAAK;sDAAK;;;;;;;;;;;kDAIpB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU9C,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAkD;;;;;;8CAChE,8OAAC;oCAAE,WAAU;8CAAmE;;;;;;8CAGhF,8OAAC;oCAAE,WAAU;8CAAuC;;;;;;;;;;;;sCAItD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,SAAQ;4DAAY,aAAY;4DAAM,QAAO;sEAC3F,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,GAAE;;;;;;;;;;;;;;;;oDAEnD;;;;;;;0DAGR,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,SAAQ;4DAAY,aAAY;4DAAM,QAAO;sEAC3F,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,GAAE;;;;;;;;;;;;;;;;oDAEnD;;;;;;;0DAGR,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,SAAQ;4DAAY,aAAY;4DAAM,QAAO;sEAC3F,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,GAAE;;;;;;;;;;;;;;;;oDAEnD;;;;;;;0DAGR,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;;;;;;;kDAIzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAO,SAAQ;4DAAY,aAAY;4DAAM,QAAO;sEAC3F,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,GAAE;;;;;;;;;;;;;;;;oDAEnD;;;;;;;0DAGR,8OAAC;gDAAG,WAAU;0DAAyC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjE,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA2D;;;;;;0CAGzE,8OAAC;gCAAE,WAAU;0CAAwD;;;;;;0CAGrE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;4CAAC,MAAK;sDAAK;;;;;;;;;;;kDAIpB,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;;4CAA6C;0DAChE,8OAAC;gDAAK,eAAY;0DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpD", "debugId": null}}]}