{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number | string): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n  }).format(Number(price))\n}\n\nexport function formatDate(date: Date | string): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function generateSlug(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).trim() + '...'\n}\n\nexport function generateOrderNumber(): string {\n  const timestamp = Date.now().toString()\n  const random = Math.random().toString(36).substring(2, 8).toUpperCase()\n  return `FBZ-${timestamp.slice(-6)}-${random}`\n}\n\nexport function generateAffiliateCode(username: string): string {\n  const random = Math.random().toString(36).substring(2, 6).toUpperCase()\n  const userPart = username.slice(0, 4).toUpperCase()\n  return `${userPart}${random}`\n}\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAsB;IAChD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC,OAAO;AACnB;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,aAAa,IAAY;IACvC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,IAAI,KAAK;AAC3C;AAEO,SAAS;IACd,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ;IACrC,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;IACrE,OAAO,CAAC,IAAI,EAAE,UAAU,KAAK,CAAC,CAAC,GAAG,CAAC,EAAE,QAAQ;AAC/C;AAEO,SAAS,sBAAsB,QAAgB;IACpD,MAAM,SAAS,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,GAAG,WAAW;IACrE,MAAM,WAAW,SAAS,KAAK,CAAC,GAAG,GAAG,WAAW;IACjD,OAAO,GAAG,WAAW,QAAQ;AAC/B", "debugId": null}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\ninterface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'destructive'\n  size?: 'sm' | 'md' | 'lg'\n  isLoading?: boolean\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'primary', size = 'md', isLoading, children, disabled, ...props }, ref) => {\n    const baseClasses = 'inline-flex items-center justify-center rounded-lg font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed'\n    \n    const variants = {\n      primary: 'bg-gradient-to-r from-blue-600 to-purple-600 text-white hover:from-blue-700 hover:to-purple-700 focus:ring-blue-500',\n      secondary: 'bg-gray-100 text-gray-800 hover:bg-gray-200 focus:ring-gray-500',\n      outline: 'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500',\n      ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',\n      destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'\n    }\n    \n    const sizes = {\n      sm: 'px-3 py-2 text-sm',\n      md: 'px-6 py-3 text-base',\n      lg: 'px-8 py-4 text-lg'\n    }\n\n    return (\n      <button\n        ref={ref}\n        className={cn(\n          baseClasses,\n          variants[variant],\n          sizes[size],\n          isLoading && 'cursor-wait',\n          className\n        )}\n        disabled={disabled || isLoading}\n        {...props}\n      >\n        {isLoading ? (\n          <>\n            <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n            Loading...\n          </>\n        ) : (\n          children\n        )}\n      </button>\n    )\n  }\n)\n\nButton.displayName = 'Button'\n\nexport default Button\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAQA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,MAAM,cAAc;IAEpB,MAAM,WAAW;QACf,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,aAAa;IACf;IAEA,MAAM,QAAQ;QACZ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,aACA,QAAQ,CAAC,QAAQ,EACjB,KAAK,CAAC,KAAK,EACX,aAAa,eACb;QAEF,UAAU,YAAY;QACrB,GAAG,KAAK;kBAER,0BACC;;8BACE,8OAAC;oBAAI,WAAU;oBAAkC,OAAM;oBAA6B,MAAK;oBAAO,SAAQ;;sCACtG,8OAAC;4BAAO,WAAU;4BAAa,IAAG;4BAAK,IAAG;4BAAK,GAAE;4BAAK,QAAO;4BAAe,aAAY;;;;;;sCACxF,8OAAC;4BAAK,WAAU;4BAAa,MAAK;4BAAe,GAAE;;;;;;;;;;;;gBAC/C;;2BAIR;;;;;;AAIR;AAGF,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { useSession, signOut } from 'next-auth/react'\nimport { Bars3Icon, XMarkIcon, ShoppingCartIcon, UserIcon } from '@heroicons/react/24/outline'\nimport Button from '@/components/ui/Button'\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'Shop', href: '/shop' },\n  { name: 'Forex EA', href: '/category/forex-ea' },\n  { name: 'Indicators', href: '/category/indicators' },\n  { name: 'Services', href: '/services' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/contact' },\n]\n\nexport default function Header() {\n  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)\n  const { data: session, status } = useSession()\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <nav className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\" aria-label=\"Top\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">FBZ</span>\n              </div>\n              <span className=\"text-xl font-bold gradient-text\">Forex Bot Zone</span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex lg:items-center lg:space-x-8\">\n            {navigation.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors\"\n              >\n                {item.name}\n              </Link>\n            ))}\n          </div>\n\n          {/* Right side */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Search */}\n            <div className=\"hidden md:block\">\n              <div className=\"relative\">\n                <input\n                  type=\"text\"\n                  placeholder=\"Search products...\"\n                  className=\"w-64 px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n                />\n                <div className=\"absolute inset-y-0 right-0 flex items-center pr-3\">\n                  <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                  </svg>\n                </div>\n              </div>\n            </div>\n\n            {/* Cart */}\n            <Link href=\"/cart\" className=\"relative p-2 text-gray-700 hover:text-blue-600 transition-colors\">\n              <ShoppingCartIcon className=\"h-6 w-6\" />\n              <span className=\"absolute -top-1 -right-1 h-5 w-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                0\n              </span>\n            </Link>\n\n            {/* User Menu */}\n            {status === 'loading' ? (\n              <div className=\"h-8 w-8 bg-gray-200 rounded-full animate-pulse\"></div>\n            ) : session ? (\n              <div className=\"relative group\">\n                <button className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors\">\n                  {session.user.image ? (\n                    <img src={session.user.image} alt=\"\" className=\"h-8 w-8 rounded-full\" />\n                  ) : (\n                    <UserIcon className=\"h-6 w-6 text-gray-700\" />\n                  )}\n                  <span className=\"hidden md:block text-sm font-medium text-gray-700\">\n                    {session.user.name || session.user.email}\n                  </span>\n                </button>\n                \n                {/* Dropdown Menu */}\n                <div className=\"absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                  <div className=\"py-1\">\n                    <Link href=\"/dashboard\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      Dashboard\n                    </Link>\n                    <Link href=\"/profile\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      Profile\n                    </Link>\n                    <Link href=\"/orders\" className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\">\n                      Orders\n                    </Link>\n                    <hr className=\"my-1\" />\n                    <button\n                      onClick={() => signOut()}\n                      className=\"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                    >\n                      Sign out\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Link href=\"/auth/signin\">\n                  <Button variant=\"ghost\" size=\"sm\">Sign in</Button>\n                </Link>\n                <Link href=\"/auth/signup\">\n                  <Button size=\"sm\">Sign up</Button>\n                </Link>\n              </div>\n            )}\n\n            {/* Mobile menu button */}\n            <button\n              type=\"button\"\n              className=\"lg:hidden p-2 text-gray-700 hover:text-blue-600 transition-colors\"\n              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}\n            >\n              {mobileMenuOpen ? (\n                <XMarkIcon className=\"h-6 w-6\" />\n              ) : (\n                <Bars3Icon className=\"h-6 w-6\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile menu */}\n        {mobileMenuOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"space-y-1 pb-3 pt-2\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md\"\n                  onClick={() => setMobileMenuOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </nav>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAY,MAAM;IAAqB;IAC/C;QAAE,MAAM;QAAc,MAAM;IAAuB;IACnD;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAE3C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;YAAyC,cAAW;;8BACjE,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,8OAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,8OAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;8CAET,KAAK,IAAI;mCAJL,KAAK,IAAI;;;;;;;;;;sCAUpB,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,aAAY;gDACZ,WAAU;;;;;;0DAEZ,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;oDAAwB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC/E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAO7E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAQ,WAAU;;sDAC3B,8OAAC,+NAAA,CAAA,mBAAgB;4CAAC,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,WAAU;sDAA+G;;;;;;;;;;;;gCAMhI,WAAW,0BACV,8OAAC;oCAAI,WAAU;;;;;2CACb,wBACF,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;;gDACf,QAAQ,IAAI,CAAC,KAAK,iBACjB,8OAAC;oDAAI,KAAK,QAAQ,IAAI,CAAC,KAAK;oDAAE,KAAI;oDAAG,WAAU;;;;;yEAE/C,8OAAC,+MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DAEtB,8OAAC;oDAAK,WAAU;8DACb,QAAQ,IAAI,CAAC,IAAI,IAAI,QAAQ,IAAI,CAAC,KAAK;;;;;;;;;;;;sDAK5C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAa,WAAU;kEAA0D;;;;;;kEAG5F,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAA0D;;;;;;kEAG1F,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAU,WAAU;kEAA0D;;;;;;kEAGzF,8OAAC;wDAAG,WAAU;;;;;;kEACd,8OAAC;wDACC,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;wDACrB,WAAU;kEACX;;;;;;;;;;;;;;;;;;;;;;yDAOP,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;gDAAC,SAAQ;gDAAQ,MAAK;0DAAK;;;;;;;;;;;sDAEpC,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,kIAAA,CAAA,UAAM;gDAAC,MAAK;0DAAK;;;;;;;;;;;;;;;;;8CAMxB,8OAAC;oCACC,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,kBAAkB,CAAC;8CAEjC,+BACC,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAErB,8OAAC,iNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAO5B,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,kBAAkB;0CAEhC,KAAK,IAAI;+BALL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC", "debugId": null}}, {"offset": {"line": 581, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/components/layout/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nconst footerNavigation = {\n  categories: [\n    { name: 'Forex EA', href: '/category/forex-ea' },\n    { name: 'MT4 Indicators', href: '/category/mt4-indicators' },\n    { name: 'MT5 Indicators', href: '/category/mt5-indicators' },\n    { name: 'Trading Tools', href: '/category/tools' },\n  ],\n  services: [\n    { name: 'Custom EA Development', href: '/services/custom-ea' },\n    { name: 'Decompile Service', href: '/services/decompile' },\n    { name: 'Copy Trading Setup', href: '/services/copy-trading' },\n    { name: 'VPS Hosting', href: '/services/vps' },\n  ],\n  support: [\n    { name: 'Contact Us', href: '/contact' },\n    { name: 'FAQ', href: '/faq' },\n    { name: 'Knowledge Base', href: '/knowledge-base' },\n    { name: 'Affiliate Program', href: '/affiliate' },\n  ],\n  legal: [\n    { name: 'Privacy Policy', href: '/privacy' },\n    { name: 'Terms of Service', href: '/terms' },\n    { name: 'Refund Policy', href: '/refund' },\n    { name: 'DMCA', href: '/dmca' },\n  ],\n}\n\nconst socialLinks = [\n  {\n    name: 'YouTube',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path d=\"M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z\"/>\n      </svg>\n    ),\n  },\n  {\n    name: 'Facebook',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path fillRule=\"evenodd\" d=\"M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z\" clipRule=\"evenodd\" />\n      </svg>\n    ),\n  },\n  {\n    name: 'Telegram',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path d=\"M11.944 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0a12 12 0 0 0-.056 0zm4.962 7.224c.1-.002.321.023.465.14a.506.506 0 0 1 .171.325c.016.093.036.306.02.472-.18 1.898-.962 6.502-1.36 8.627-.168.9-.499 1.201-.82 1.23-.696.065-1.225-.46-1.9-.902-1.056-.693-1.653-1.124-2.678-1.8-1.185-.78-.417-1.21.258-1.91.177-.184 3.247-2.977 3.307-3.23.007-.032.014-.15-.056-.212s-.174-.041-.249-.024c-.106.024-1.793 1.14-5.061 3.345-.48.33-.913.49-1.302.48-.428-.008-1.252-.241-1.865-.44-.752-.245-1.349-.374-1.297-.789.027-.216.325-.437.893-.663 3.498-1.524 5.83-2.529 6.998-3.014 3.332-1.386 4.025-1.627 4.476-1.635z\"/>\n      </svg>\n    ),\n  },\n  {\n    name: 'Pinterest',\n    href: '#',\n    icon: (props: any) => (\n      <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" {...props}>\n        <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 5.079 3.158 9.417 7.618 11.174-.105-.949-.199-2.403.041-3.439.219-.937 1.406-5.957 1.406-5.957s-.359-.72-.359-1.781c0-1.663.967-2.911 2.168-2.911 1.024 0 1.518.769 1.518 1.688 0 1.029-.653 2.567-.992 3.992-.285 1.193.6 2.165 1.775 2.165 2.128 0 3.768-2.245 3.768-5.487 0-2.861-2.063-4.869-5.008-4.869-3.41 0-5.409 2.562-5.409 5.199 0 1.033.394 2.143.889 2.741.099.12.112.225.085.345-.09.375-.293 1.199-.334 1.363-.053.225-.172.271-.402.165-1.495-.69-2.433-2.878-2.433-4.646 0-3.776 2.748-7.252 7.92-7.252 4.158 0 7.392 2.967 7.392 6.923 0 4.135-2.607 7.462-6.233 7.462-1.214 0-2.357-.629-2.746-1.378l-.748 2.853c-.271 1.043-1.002 2.35-1.492 3.146C9.57 23.812 10.763 24.009 12.017 24c6.624 0 11.99-5.367 11.99-12C24.007 5.367 18.641.001 12.017.001z\"/>\n      </svg>\n    ),\n  },\n]\n\nexport default function Footer() {\n  return (\n    <footer className=\"bg-gray-900\" aria-labelledby=\"footer-heading\">\n      <h2 id=\"footer-heading\" className=\"sr-only\">\n        Footer\n      </h2>\n      <div className=\"mx-auto max-w-7xl px-6 pb-8 pt-16 sm:pt-24 lg:px-8 lg:pt-32\">\n        <div className=\"xl:grid xl:grid-cols-3 xl:gap-8\">\n          <div className=\"space-y-8\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">FBZ</span>\n              </div>\n              <span className=\"text-xl font-bold text-white\">Forex Bot Zone</span>\n            </div>\n            <p className=\"text-sm leading-6 text-gray-300\">\n              Your trusted source for premium forex trading tools, expert advisors, and indicators. \n              Trade smart, trade confident with our professional-grade solutions.\n            </p>\n            <div className=\"flex space-x-6\">\n              {socialLinks.map((item) => (\n                <a key={item.name} href={item.href} className=\"text-gray-400 hover:text-gray-300\">\n                  <span className=\"sr-only\">{item.name}</span>\n                  <item.icon className=\"h-6 w-6\" aria-hidden=\"true\" />\n                </a>\n              ))}\n            </div>\n          </div>\n          <div className=\"mt-16 grid grid-cols-2 gap-8 xl:col-span-2 xl:mt-0\">\n            <div className=\"md:grid md:grid-cols-2 md:gap-8\">\n              <div>\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">Categories</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {footerNavigation.categories.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n              <div className=\"mt-10 md:mt-0\">\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">Services</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {footerNavigation.services.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n            <div className=\"md:grid md:grid-cols-2 md:gap-8\">\n              <div>\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">Support</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {footerNavigation.support.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n              <div className=\"mt-10 md:mt-0\">\n                <h3 className=\"text-sm font-semibold leading-6 text-white\">Legal</h3>\n                <ul role=\"list\" className=\"mt-6 space-y-4\">\n                  {footerNavigation.legal.map((item) => (\n                    <li key={item.name}>\n                      <Link href={item.href} className=\"text-sm leading-6 text-gray-300 hover:text-white\">\n                        {item.name}\n                      </Link>\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-16 border-t border-gray-700 pt-8 sm:mt-20 lg:mt-24\">\n          <div className=\"md:flex md:items-center md:justify-between\">\n            <div className=\"flex space-x-6 md:order-2\">\n              <p className=\"text-xs leading-5 text-gray-400\">\n                Contact: <EMAIL>\n              </p>\n            </div>\n            <p className=\"mt-8 text-xs leading-5 text-gray-400 md:order-1 md:mt-0\">\n              &copy; 2024 Forex Bot Zone. All rights reserved.\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,MAAM,mBAAmB;IACvB,YAAY;QACV;YAAE,MAAM;YAAY,MAAM;QAAqB;QAC/C;YAAE,MAAM;YAAkB,MAAM;QAA2B;QAC3D;YAAE,MAAM;YAAkB,MAAM;QAA2B;QAC3D;YAAE,MAAM;YAAiB,MAAM;QAAkB;KAClD;IACD,UAAU;QACR;YAAE,MAAM;YAAyB,MAAM;QAAsB;QAC7D;YAAE,MAAM;YAAqB,MAAM;QAAsB;QACzD;YAAE,MAAM;YAAsB,MAAM;QAAyB;QAC7D;YAAE,MAAM;YAAe,MAAM;QAAgB;KAC9C;IACD,SAAS;QACP;YAAE,MAAM;YAAc,MAAM;QAAW;QACvC;YAAE,MAAM;YAAO,MAAM;QAAO;QAC5B;YAAE,MAAM;YAAkB,MAAM;QAAkB;QAClD;YAAE,MAAM;YAAqB,MAAM;QAAa;KACjD;IACD,OAAO;QACL;YAAE,MAAM;YAAkB,MAAM;QAAW;QAC3C;YAAE,MAAM;YAAoB,MAAM;QAAS;QAC3C;YAAE,MAAM;YAAiB,MAAM;QAAU;QACzC;YAAE,MAAM;YAAQ,MAAM;QAAQ;KAC/B;AACH;AAEA,MAAM,cAAc;IAClB;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;IAGd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBAAK,UAAS;oBAAU,GAAE;oBAAyQ,UAAS;;;;;;;;;;;IAGnT;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;IAGd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,CAAC,sBACL,8OAAC;gBAAI,MAAK;gBAAe,SAAQ;gBAAa,GAAG,KAAK;0BACpD,cAAA,8OAAC;oBAAK,GAAE;;;;;;;;;;;IAGd;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAO,WAAU;QAAc,mBAAgB;;0BAC9C,8OAAC;gBAAG,IAAG;gBAAiB,WAAU;0BAAU;;;;;;0BAG5C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEjD,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;kDAEjD,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;kDAI/C,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;gDAAkB,MAAM,KAAK,IAAI;gDAAE,WAAU;;kEAC5C,8OAAC;wDAAK,WAAU;kEAAW,KAAK,IAAI;;;;;;kEACpC,8OAAC,KAAK,IAAI;wDAAC,WAAU;wDAAU,eAAY;;;;;;;+CAFrC,KAAK,IAAI;;;;;;;;;;;;;;;;0CAOvB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,iBAAiB,UAAU,CAAC,GAAG,CAAC,CAAC,qBAChC,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;0DAQxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,iBAAiB,QAAQ,CAAC,GAAG,CAAC,CAAC,qBAC9B,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;kDAS1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,iBAAiB,OAAO,CAAC,GAAG,CAAC,CAAC,qBAC7B,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;0DAQxB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA6C;;;;;;kEAC3D,8OAAC;wDAAG,MAAK;wDAAO,WAAU;kEACvB,iBAAiB,KAAK,CAAC,GAAG,CAAC,CAAC,qBAC3B,8OAAC;0EACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oEAAC,MAAM,KAAK,IAAI;oEAAE,WAAU;8EAC9B,KAAK,IAAI;;;;;;+DAFL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW9B,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;8CAIjD,8OAAC;oCAAE,WAAU;8CAA0D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnF", "debugId": null}}, {"offset": {"line": 1095, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/components/layout/Layout.tsx"], "sourcesContent": ["'use client'\n\nimport { SessionProvider } from 'next-auth/react'\nimport Header from './Header'\nimport Footer from './Footer'\n\ninterface LayoutProps {\n  children: React.ReactNode\n  session?: any\n}\n\nexport default function Layout({ children, session }: LayoutProps) {\n  return (\n    <SessionProvider session={session}>\n      <div className=\"min-h-screen flex flex-col\">\n        <Header />\n        <main className=\"flex-1\">\n          {children}\n        </main>\n        <Footer />\n      </div>\n    </SessionProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAe;IAC/D,qBACE,8OAAC,8IAAA,CAAA,kBAAe;QAAC,SAAS;kBACxB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,UAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf", "debugId": null}}]}