{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { withAuth } from \"next-auth/middleware\"\n\nexport default withAuth(\n  function middleware(req) {\n    // Add any additional middleware logic here\n  },\n  {\n    callbacks: {\n      authorized: ({ token, req }) => {\n        // Check if user is trying to access protected routes\n        if (req.nextUrl.pathname.startsWith('/dashboard')) {\n          return !!token\n        }\n        if (req.nextUrl.pathname.startsWith('/admin')) {\n          return token?.role === 'ADMIN'\n        }\n        return true\n      },\n    },\n  }\n)\n\nexport const config = {\n  matcher: ['/dashboard/:path*', '/admin/:path*']\n}\n"], "names": [], "mappings": ";;;;AAAA;;uCAEe,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EACpB,SAAS,WAAW,GAAG;AACrB,2CAA2C;AAC7C,GACA;IACE,WAAW;QACT,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,EAAE;YACzB,qDAAqD;YACrD,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe;gBACjD,OAAO,CAAC,CAAC;YACX;YACA,IAAI,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,WAAW;gBAC7C,OAAO,OAAO,SAAS;YACzB;YACA,OAAO;QACT;IACF;AACF;AAGK,MAAM,SAAS;IACpB,SAAS;QAAC;QAAqB;KAAgB;AACjD"}}]}