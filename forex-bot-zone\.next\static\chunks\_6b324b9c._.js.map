{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/src/app/auth/signin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { signIn, getSession } from 'next-auth/react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\nimport Button from '@/components/ui/Button'\n\nexport default function SignIn() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [error, setError] = useState('')\n  const router = useRouter()\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setIsLoading(true)\n    setError('')\n\n    try {\n      const result = await signIn('credentials', {\n        email,\n        password,\n        redirect: false,\n      })\n\n      if (result?.error) {\n        setError('Invalid email or password')\n      } else {\n        // Refresh the session and redirect\n        await getSession()\n        router.push('/dashboard')\n        router.refresh()\n      }\n    } catch (error) {\n      setError('An error occurred. Please try again.')\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const handleGoogleSignIn = () => {\n    signIn('google', { callbackUrl: '/dashboard' })\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8\">\n      <div className=\"max-w-md w-full space-y-8\">\n        <div>\n          <div className=\"flex justify-center\">\n            <div className=\"h-12 w-12 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">FBZ</span>\n            </div>\n          </div>\n          <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n            Sign in to your account\n          </h2>\n          <p className=\"mt-2 text-center text-sm text-gray-600\">\n            Or{' '}\n            <Link href=\"/auth/signup\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n              create a new account\n            </Link>\n          </p>\n        </div>\n        \n        <form className=\"mt-8 space-y-6\" onSubmit={handleSubmit}>\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm\">\n              {error}\n            </div>\n          )}\n          \n          <div className=\"space-y-4\">\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700\">\n                Email address\n              </label>\n              <input\n                id=\"email\"\n                name=\"email\"\n                type=\"email\"\n                autoComplete=\"email\"\n                required\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your email\"\n              />\n            </div>\n            \n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700\">\n                Password\n              </label>\n              <input\n                id=\"password\"\n                name=\"password\"\n                type=\"password\"\n                autoComplete=\"current-password\"\n                required\n                value={password}\n                onChange={(e) => setPassword(e.target.value)}\n                className=\"mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm\"\n                placeholder=\"Enter your password\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center\">\n              <input\n                id=\"remember-me\"\n                name=\"remember-me\"\n                type=\"checkbox\"\n                className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n              />\n              <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n                Remember me\n              </label>\n            </div>\n\n            <div className=\"text-sm\">\n              <Link href=\"/auth/forgot-password\" className=\"font-medium text-blue-600 hover:text-blue-500\">\n                Forgot your password?\n              </Link>\n            </div>\n          </div>\n\n          <div>\n            <Button\n              type=\"submit\"\n              isLoading={isLoading}\n              className=\"group relative w-full flex justify-center\"\n            >\n              Sign in\n            </Button>\n          </div>\n\n          <div className=\"mt-6\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-0 flex items-center\">\n                <div className=\"w-full border-t border-gray-300\" />\n              </div>\n              <div className=\"relative flex justify-center text-sm\">\n                <span className=\"px-2 bg-gray-50 text-gray-500\">Or continue with</span>\n              </div>\n            </div>\n\n            <div className=\"mt-6\">\n              <Button\n                type=\"button\"\n                variant=\"outline\"\n                onClick={handleGoogleSignIn}\n                className=\"w-full flex justify-center items-center\"\n              >\n                <svg className=\"w-5 h-5 mr-2\" viewBox=\"0 0 24 24\">\n                  <path fill=\"currentColor\" d=\"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z\"/>\n                  <path fill=\"currentColor\" d=\"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z\"/>\n                  <path fill=\"currentColor\" d=\"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z\"/>\n                  <path fill=\"currentColor\" d=\"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z\"/>\n                </svg>\n                Sign in with Google\n              </Button>\n            </div>\n          </div>\n        </form>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBACzC;gBACA;gBACA,UAAU;YACZ;YAEA,IAAI,mBAAA,6BAAA,OAAQ,KAAK,EAAE;gBACjB,SAAS;YACX,OAAO;gBACL,mCAAmC;gBACnC,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;gBACf,OAAO,IAAI,CAAC;gBACZ,OAAO,OAAO;YAChB;QACF,EAAE,OAAO,OAAO;YACd,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,qBAAqB;QACzB,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU;YAAE,aAAa;QAAa;IAC/C;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;;sCACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;sCAGnD,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;;gCAAyC;gCACjD;8CACH,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAe,WAAU;8CAAgD;;;;;;;;;;;;;;;;;;8BAMxF,6LAAC;oBAAK,WAAU;oBAAiB,UAAU;;wBACxC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAA0C;;;;;;sDAG3E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAA0C;;;;;;sDAG9E,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,cAAa;4CACb,QAAQ;4CACR,OAAO;4CACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;4CAC3C,WAAU;4CACV,aAAY;;;;;;;;;;;;;;;;;;sCAKlB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,IAAG;4CACH,MAAK;4CACL,MAAK;4CACL,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAc,WAAU;sDAAmC;;;;;;;;;;;;8CAK5E,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAwB,WAAU;kDAAgD;;;;;;;;;;;;;;;;;sCAMjG,6LAAC;sCACC,cAAA,6LAAC,qIAAA,CAAA,UAAM;gCACL,MAAK;gCACL,WAAW;gCACX,WAAU;0CACX;;;;;;;;;;;sCAKH,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAgC;;;;;;;;;;;;;;;;;8CAIpD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,UAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;gDAAe,SAAQ;;kEACpC,6LAAC;wDAAK,MAAK;wDAAe,GAAE;;;;;;kEAC5B,6LAAC;wDAAK,MAAK;wDAAe,GAAE;;;;;;kEAC5B,6LAAC;wDAAK,MAAK;wDAAe,GAAE;;;;;;kEAC5B,6LAAC;wDAAK,MAAK;wDAAe,GAAE;;;;;;;;;;;;4CACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB;GAlKwB;;QAKP,qIAAA,CAAA,YAAS;;;KALF", "debugId": null}}, {"offset": {"line": 420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/forexbotzone/forex-bot-zone/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}